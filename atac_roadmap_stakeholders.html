<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal ATAC - Roadmap de Implementação</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 40px;
            margin-bottom: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            border-radius: 15px 15px 0 0;
            margin: -20px -20px 40px -20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .executive-summary {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .status-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .status-card {
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-complete {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
        }

        .status-partial {
            background: linear-gradient(135deg, #FF9800, #FFB74D);
            color: white;
        }

        .status-missing {
            background: linear-gradient(135deg, #F44336, #EF5350);
            color: white;
        }

        .phase {
            margin: 40px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .phase-1 {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            color: white;
        }

        .phase-2 {
            background: linear-gradient(135deg, #FFB74D, #FFD54F);
            color: #333;
        }

        .phase-3 {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
        }

        .phase h2 {
            font-size: 2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .phase-icon {
            font-size: 1.2em;
        }

        .timeline {
            display: grid;
            gap: 20px;
            margin: 20px 0;
        }

        .milestone {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid rgba(255,255,255,0.5);
        }

        .milestone h4 {
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .milestone-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .budget {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .roi {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .investment-section {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin: 40px 0;
        }

        .investment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .investment-card {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }

        .investment-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin: 15px 0;
        }

        .metrics-section {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin: 40px 0;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .metric-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
            color: #f39c12;
        }

        .next-steps {
            background: linear-gradient(135deg, #16a085, #1abc9c);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 40px 0;
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .step-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }

        .cta-section {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 15px;
            margin: 40px 0;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: #e74c3c;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.2em;
            margin: 20px 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            background: rgba(255,255,255,0.2);
            height: 10px;
            border-radius: 5px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #66BB6A);
            border-radius: 5px;
            transition: width 0.5s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .milestone-meta {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ Portal ATAC</h1>
            <div class="subtitle">Roadmap de Implementação Estratégica</div>
        </div>

        <div class="executive-summary fade-in">
            <h2>📊 Resumo Executivo</h2>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 72%"></div>
            </div>
            <p><strong>Status Atual: 72% de Conformidade</strong> (13/18 requisitos atendidos)</p>
            <p><strong>Meta:</strong> Atingir 95%+ de conformidade em 12 meses</p>
            <p><strong>Investimento Total:</strong> R$ 2,4M | <strong>ROI Projetado:</strong> +400% em 24 meses</p>
        </div>

        <div class="status-cards fade-in">
            <div class="status-card status-complete">
                <h3>✅ 8 Requisitos</h3>
                <p><strong>Totalmente Atendidos</strong></p>
                <p>Jornadas personalizadas, frameworks práticos, cases de sucesso</p>
            </div>
            <div class="status-card status-partial">
                <h3>⚠️ 7 Requisitos</h3>
                <p><strong>Parcialmente Atendidos</strong></p>
                <p>Perfis específicos, venda direta, personalização</p>
            </div>
            <div class="status-card status-missing">
                <h3>❌ 3 Requisitos</h3>
                <p><strong>Não Atendidos</strong></p>
                <p>Metodologia ATAC, continuidade, personalização avançada</p>
            </div>
        </div>

        <div class="phase phase-1 fade-in">
            <h2><span class="phase-icon">🔴</span> FASE 1: Alta Prioridade</h2>
            <p><strong>Período:</strong> Meses 1-8 | <strong>Impacto:</strong> +40% conversão, +60% engajamento</p>
            
            <div class="timeline">
                <div class="milestone">
                    <div class="milestone-meta">
                        <h4>Metodologia ATAC</h4>
                        <div class="budget">R$ 50.000</div>
                        <div class="roi">ROI: 200%</div>
                    </div>
                    <p><strong>Meses 1-2:</strong> Página completa, vídeo apresentação, infográfico interativo</p>
                    <p><strong>Meta:</strong> Tempo na página > 5 min, Conversão > 15%</p>
                </div>

                <div class="milestone">
                    <div class="milestone-meta">
                        <h4>Perfis de Usuário</h4>
                        <div class="budget">R$ 80.000</div>
                        <div class="roi">ROI: 250%</div>
                    </div>
                    <p><strong>Meses 3-4:</strong> Onboarding personalizado, 3 dashboards específicos</p>
                    <p><strong>Meta:</strong> Conclusão onboarding > 80%, Retenção > 70%</p>
                </div>

                <div class="milestone">
                    <div class="milestone-meta">
                        <h4>Catálogo de Cursos</h4>
                        <div class="budget">R$ 120.000</div>
                        <div class="roi">ROI: 400%</div>
                    </div>
                    <p><strong>Meses 5-6:</strong> 12 cursos completos, 3 trilhas de aprendizado</p>
                    <p><strong>Meta:</strong> Vendas > R$ 50k/mês, Conclusão > 70%</p>
                </div>

                <div class="milestone">
                    <div class="milestone-meta">
                        <h4>Demonstração do Método</h4>
                        <div class="budget">R$ 60.000</div>
                        <div class="roi">ROI: 300%</div>
                    </div>
                    <p><strong>Meses 7-8:</strong> Simulador interativo, cases documentados</p>
                    <p><strong>Meta:</strong> Finalização > 90%, Conversão > 25%</p>
                </div>
            </div>
        </div>

        <div class="phase phase-2 fade-in">
            <h2><span class="phase-icon">🟡</span> FASE 2: Média Prioridade</h2>
            <p><strong>Período:</strong> Meses 9-18 | <strong>Impacto:</strong> +30% retenção, +50% LTV</p>
            
            <div class="timeline">
                <div class="milestone">
                    <h4>Algoritmo de Recomendação</h4>
                    <p><strong>Meses 9-10:</strong> Sistema de tracking, collaborative filtering</p>
                </div>

                <div class="milestone">
                    <h4>Otimização de Compra</h4>
                    <p><strong>Meses 11-12:</strong> Checkout 2-3 cliques, compra com um clique</p>
                </div>

                <div class="milestone">
                    <h4>Analytics e A/B Testing</h4>
                    <p><strong>Meses 13-14:</strong> Dashboard completo, funil de conversão</p>
                </div>

                <div class="milestone">
                    <h4>Perfis de Especialistas</h4>
                    <p><strong>Meses 15-16:</strong> Páginas detalhadas, avaliações, agendamento</p>
                </div>

                <div class="milestone">
                    <h4>CRM Básico</h4>
                    <p><strong>Meses 17-18:</strong> Segmentação, automação de email</p>
                </div>
            </div>
        </div>

        <div class="phase phase-3 fade-in">
            <h2><span class="phase-icon">🟢</span> FASE 3: Baixa Prioridade</h2>
            <p><strong>Período:</strong> Meses 19-30 | <strong>Impacto:</strong> +100% eficiência, liderança tecnológica</p>
            
            <div class="timeline">
                <div class="milestone">
                    <h4>Machine Learning</h4>
                    <p><strong>Trimestre 7-8:</strong> Pipeline ML, predição de churn, personalização avançada</p>
                </div>

                <div class="milestone">
                    <h4>Gamificação</h4>
                    <p><strong>Trimestre 9:</strong> Sistema de pontos, badges, leaderboards sociais</p>
                </div>

                <div class="milestone">
                    <h4>Comunicação Automatizada</h4>
                    <p><strong>Trimestre 10:</strong> Multi-canal, fluxos automatizados, ML personalização</p>
                </div>

                <div class="milestone">
                    <h4>Integrações</h4>
                    <p><strong>Trimestre 11:</strong> 5 integrações principais, webhooks, API marketplace</p>
                </div>

                <div class="milestone">
                    <h4>Mobile App</h4>
                    <p><strong>Trimestre 12:</strong> App nativo iOS/Android, modo offline</p>
                </div>
            </div>
        </div>

        <div class="investment-section fade-in">
            <h2>💰 Investimento e ROI</h2>
            <div class="investment-grid">
                <div class="investment-card">
                    <h3>Fase 1</h3>
                    <div class="investment-value">R$ 310k</div>
                    <p>Alta Prioridade<br>Meses 1-8</p>
                </div>
                <div class="investment-card">
                    <h3>Fase 2</h3>
                    <div class="investment-value">R$ 800k</div>
                    <p>Média Prioridade<br>Meses 9-18</p>
                </div>
                <div class="investment-card">
                    <h3>Fase 3</h3>
                    <div class="investment-value">R$ 1.3M</div>
                    <p>Baixa Prioridade<br>Meses 19-30</p>
                </div>
                <div class="investment-card">
                    <h3>ROI Total</h3>
                    <div class="investment-value">+400%</div>
                    <p>24 meses<br>Sustentável</p>
                </div>
            </div>
        </div>

        <div class="metrics-section fade-in">
            <h2>📈 Métricas de Impacto</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h4>Conversão</h4>
                    <div class="metric-value">5% → 7%</div>
                    <p>+40% melhoria</p>
                </div>
                <div class="metric-card">
                    <h4>Engajamento</h4>
                    <div class="metric-value">15 → 24 min</div>
                    <p>+60% tempo na plataforma</p>
                </div>
                <div class="metric-card">
                    <h4>Receita</h4>
                    <div class="metric-value">R$100k → R$300k</div>
                    <p>+200% mensal</p>
                </div>
                <div class="metric-card">
                    <h4>NPS</h4>
                    <div class="metric-value">45 → 70+</div>
                    <p>+56% satisfação</p>
                </div>
                <div class="metric-card">
                    <h4>Retenção</h4>
                    <div class="metric-value">40% → 52%</div>
                    <p>+30% em 90 dias</p>
                </div>
                <div class="metric-card">
                    <h4>LTV</h4>
                    <div class="metric-value">R$2k → R$3k</div>
                    <p>+50% valor vitalício</p>
                </div>
            </div>
        </div>

        <div class="next-steps fade-in">
            <h2>🚀 Próximos Passos Imediatos</h2>
            <div class="steps-grid">
                <div class="step-card">
                    <h4>Semana 1-2: Preparação</h4>
                    <p>• Aprovação do roadmap<br>• Definição do orçamento<br>• Contratação da equipe<br>• Setup da infraestrutura</p>
                </div>
                <div class="step-card">
                    <h4>Semana 3-4: Kickoff</h4>
                    <p>• Início desenvolvimento metodologia<br>• Criação de conteúdo<br>• Ambiente de testes<br>• Processos de QA</p>
                </div>
                <div class="step-card">
                    <h4>Mês 1: Execução</h4>
                    <p>• Página de metodologia<br>• Vídeo de apresentação<br>• Infográfico interativo<br>• Testes de usabilidade</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Animação de entrada dos elementos
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationDelay = Math.random() * 0.3 + 's';
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Animação da barra de progresso
        setTimeout(() => {
            const progressBar = document.querySelector('.progress-fill');
            progressBar.style.width = '72%';
        }, 1000);

        // Efeito hover nas métricas
        document.querySelectorAll('.metric-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Click handlers para os botões CTA
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const text = this.textContent;
                if (text.includes('Aprovar')) {
                    alert('Obrigado! Entraremos em contato para formalizar a aprovação.');
                } else if (text.includes('Detalhes')) {
                    alert('Redirecionando para documentação técnica detalhada...');
                } else if (text.includes('Reunião')) {
                    alert('Redirecionando para agendamento de reunião executiva...');
                }
            });
        });
    </script>
</body>
</html>