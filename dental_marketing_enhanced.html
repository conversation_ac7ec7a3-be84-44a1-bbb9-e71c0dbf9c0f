<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estratégia Go-to-Market - Automação Odontológica</title>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1a202c;
            line-height: 1.6;
        }

        /* PDF-friendly styles */
        @media print {
            body {
                background: white;
                font-size: 12px;
            }

            .header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .page-break {
                page-break-before: always;
            }

            .box,
            .card,
            .timeline-item,
            .pricing-card {
                break-inside: avoid;
            }
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 800;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.3em;
            font-weight: 300;
            margin-top: 10px;
            opacity: 0.95;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 60px 30px;
        }

        section {
            margin-bottom: 80px;
        }

        h2 {
            font-size: 2.2em;
            color: #2d3748;
            border-left: 6px solid #667eea;
            padding-left: 20px;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .box,
        .card,
        .timeline-item,
        .pricing-card {
            background: #ffffff;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.8);
            position: relative;
            transition: all 0.3s ease;
        }

        .card:hover,
        .box:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
        }

        .timeline-item {
            border-left: 4px solid #667eea;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .timeline-item h3 {
            color: #667eea;
            font-size: 1.4em;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .card h3,
        .card h4 {
            margin-top: 0;
            color: #2d3748;
            font-weight: 600;
        }

        .card h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .checklist,
        ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .checklist li,
        ul li {
            padding: 8px 0;
            position: relative;
            padding-left: 30px;
            font-weight: 400;
        }

        .checklist li::before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
            font-size: 1.2em;
            position: absolute;
            left: 0;
            top: 8px;
        }

        ul li::before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            font-size: 1.2em;
            position: absolute;
            left: 0;
            top: 8px;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        /* Enhanced pricing cards */
        .pricing-section .grid-3 .card {
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .pricing-section .card h4 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin: -30px -30px 20px -30px;
            padding: 20px 30px;
            font-size: 1.3em;
            border-bottom: none;
        }

        .pricing-section .card:nth-child(2) {
            transform: scale(1.05);
            border: 3px solid #667eea;
        }

        .pricing-section .card:nth-child(2)::before {
            content: 'MAIS POPULAR';
            position: absolute;
            top: 10px;
            right: -30px;
            background: #f56565;
            color: white;
            padding: 5px 40px;
            font-size: 0.8em;
            font-weight: bold;
            transform: rotate(45deg);
        }

        /* Stats cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 25px;
            border-radius: 16px;
        }

        .stats-card h3 {
            font-size: 2.5em;
            margin: 0;
            font-weight: 800;
        }

        .stats-card p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Enhanced content strategy section */
        .content-strategy .card {
            border-top: 4px solid;
            position: relative;
        }

        .content-strategy .card:nth-child(1) {
            border-top-color: #48bb78;
        }

        .content-strategy .card:nth-child(2) {
            border-top-color: #ed8936;
        }

        .content-strategy .card:nth-child(3) {
            border-top-color: #667eea;
        }

        .content-strategy .card:nth-child(4) {
            border-top-color: #f56565;
        }

        /* Investment section */
        .investment-section .card {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-left: 5px solid #667eea;
        }

        /* Enhanced emphasis blocks */
        .emphasis-block {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 16px;
            margin: 30px 0;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .emphasis-block h3 {
            margin: 0 0 15px 0;
            font-size: 1.5em;
            font-weight: 700;
        }

        .emphasis-block p {
            margin: 0;
            font-size: 1.1em;
            opacity: 0.95;
        }

        /* Process flow */
        .process-flow {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 20px 0;
        }

        .process-step {
            flex: 1;
            min-width: 150px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            position: relative;
            border: 2px solid #e2e8f0;
        }

        .process-step::after {
            content: '→';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #667eea;
            font-weight: bold;
        }

        .process-step:last-child::after {
            display: none;
        }

        .process-step h5 {
            margin: 0 0 10px 0;
            color: #667eea;
            font-weight: 600;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: white;
            padding: 40px;
            text-align: center;
            margin-top: 60px;
        }

        .footer h3 {
            margin: 0 0 20px 0;
            font-size: 1.5em;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2em;
            }

            .header p {
                font-size: 1.1em;
            }

            h2 {
                font-size: 1.8em;
            }

            .container {
                padding: 40px 20px;
            }

            .grid-2,
            .grid-3 {
                grid-template-columns: 1fr;
            }

            .process-flow {
                flex-direction: column;
            }

            .process-step::after {
                content: '↓';
                right: 50%;
                bottom: -20px;
                top: auto;
                transform: translateX(50%);
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="header-content">
            <h1>Estratégia Go-to-Market</h1>
            <p>Automação Odontológica - Plano Estratégico Completo</p>
        </div>
    </div>

    <div class="container">
        <section>
            <h2>🎯 Posicionamento da Sub-marca</h2>
            <div class="box">
                <p><strong>Nome sugerido:</strong> "Gap4x | Automação Odontológica - Powered by Agile Think"</p>
                <p><strong>Tagline:</strong> <em>"A mesma excelência em automação que levamos para grandes empresas,
                        agora especializada para clínicas odontológicas"</em></p>
                <p><strong>Proposta de valor:</strong> Transformamos clínicas odontológicas através de IA e automação,
                    combinando nossa expertise em tecnologia empresarial com profundo conhecimento do mercado
                    odontológico.</p>
            </div>
        </section>

        <div class="page-break"></div>

        <section>
            <h2>📅 Plano de Ação - Primeiros 90 Dias</h2>
            <div class="timeline-item">
                <h3>Semanas 1-2: Fundação</h3>
                <div class="grid-2">
                    <div class="card">
                        <h4>Preparação Técnica</h4>
                        <ul class="checklist">
                            <li>Agente de vendas funcionando 100%</li>
                            <li>Número WhatsApp dedicado configurado</li>
                            <li>Landing page simples</li>
                            <li>E-mail profissional configurado</li>
                        </ul>
                    </div>
                    <div class="card">
                        <h4>Preparação de Conteúdo</h4>
                        <ul class="checklist">
                            <li>Vídeo de 60s demonstrando o agente</li>
                            <li>15 posts para redes sociais</li>
                            <li>Apresentação comercial para reuniões</li>
                            <li>Definir proposta comercial</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <h3>Semanas 3-4: Validação Inicial</h3>
                <ul class="checklist">
                    <li>Testar com 5 dentistas da rede pessoal</li>
                    <li>Pedir feedback sobre produto e preço</li>
                    <li>Refinar agente baseado nos feedbacks</li>
                    <li>Conseguir 1-2 cases iniciais</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>Semanas 5-8: Tração Orgânica</h3>
                <div class="grid-3">
                    <div class="card">
                        <h4>LinkedIn</h4>
                        <ul>
                            <li>3 posts/semana</li>
                            <li>Conectar com 20 dentistas/semana</li>
                            <li>Participar de grupos</li>
                            <li>1 artigo/mês</li>
                        </ul>
                    </div>
                    <div class="card">
                        <h4>Instagram Business</h4>
                        <ul>
                            <li>Stories diários</li>
                            <li>3 posts/semana</li>
                            <li>Reels educativos</li>
                            <li>Hashtags segmentadas</li>
                        </ul>
                    </div>
                    <div class="card">
                        <h4>Facebook</h4>
                        <ul>
                            <li>Participar de grupos</li>
                            <li>Postagens com valor (1-2 por semana)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <h3>Semanas 9-12: Escala e Otimização</h3>
                <ul class="checklist">
                    <li>Analisar leads por canal</li>
                    <li>Taxa de conversão por fonte</li>
                    <li>Perfil dos clientes que mais convertem</li>
                    <li>Ajustar estratégia com base nos dados</li>
                </ul>
            </div>
        </section>

        <div class="page-break"></div>

        <section class="content-strategy">
            <h2>🧠 Estratégia de Conteúdo</h2>
            <div class="grid-2">
                <div class="card">
                    <h4>1. Educação (50%)</h4>
                    <ul>
                        <li>5 sinais que sua clínica precisa de automação</li>
                        <li>Como reduzir no-show em 60%</li>
                        <li>IA na odontologia: mito vs realidade</li>
                        <li>Paciente satisfeito = clínica próspera</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>2. Demonstração (20%)</h4>
                    <ul>
                        <li>Vídeos do agente funcionando</li>
                        <li>Cases de sucesso</li>
                        <li>Comparativos: antes vs depois</li>
                        <li>Depoimentos de clientes</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>3. Autoridade (20%)</h4>
                    <ul>
                        <li>Sua trajetória: consultoria → software → IA</li>
                        <li>Bastidores da criação do produto</li>
                        <li>Participação em eventos</li>
                        <li>Artigos técnicos</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>4. Vendas Suaves (10%)</h4>
                    <ul>
                        <li>Call-to-action: "Mande DEMO para [número]"</li>
                        <li>Ofertas de teste gratuito</li>
                        <li>Convites para webinars</li>
                        <li>Depoimentos sociais</li>
                    </ul>
                </div>
            </div>
        </section>

        <div class="page-break"></div>

        <section>
            <h2>📈 Processo Comercial</h2>
            <div class="card">
                <h4>Funil de Vendas</h4>
                <div class="process-flow">
                    <div class="process-step">
                        <h5>Atração</h5>
                        <p>Conteúdo nas redes sociais</p>
                    </div>
                    <div class="process-step">
                        <h5>Interesse</h5>
                        <p>"Mande DEMO para conhecer"</p>
                    </div>
                    <div class="process-step">
                        <h5>Consideração</h5>
                        <p>Agente demonstra produto</p>
                    </div>
                    <div class="process-step">
                        <h5>Intenção</h5>
                        <p>Agendamento de reunião</p>
                    </div>
                    <div class="process-step">
                        <h5>Compra</h5>
                        <p>Proposta personalizada + fechamento</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <h4>Reunião Comercial (30-45 min)</h4>
                <div class="grid-2">
                    <div>
                        <ul>
                            <li><strong>Rapport (5 min):</strong> Conhecer a clínica, desafios</li>
                            <li><strong>Descoberta (10 min):</strong> Dores específicas, situação atual</li>
                            <li><strong>Demonstração (15 min):</strong> Produto funcionando nas dores dele</li>
                        </ul>
                    </div>
                    <div>
                        <ul>
                            <li><strong>Proposta (10 min):</strong> Investimento personalizado</li>
                            <li><strong>Fechamento (5 min):</strong> Próximos passos</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <div class="page-break"></div>

        <section class="pricing-section">
            <h2>💳 Opções de Investimento</h2>
            <div class="grid-3">
                <div class="card">
                    <h4>Básico - R$ 397/mês</h4>
                    <ul>
                        <li>Chatbot completo (3 pilares)</li>
                        <li>Implementação padrão</li>
                        <li>Suporte por e-mail</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>Completo - R$ 497/mês</h4>
                    <ul>
                        <li>Tudo do básico</li>
                        <li>Implementação personalizada</li>
                        <li>Suporte prioritário via WhatsApp</li>
                        <li>Treinamento da equipe</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>Premium - R$ 697/mês</h4>
                    <ul>
                        <li>Tudo do completo</li>
                        <li>Dashboard de métricas</li>
                        <li>Relatórios mensais</li>
                        <li>Consultoria mensal de otimização</li>
                    </ul>
                </div>
            </div>
        </section>

        <section>
            <h2>📊 Metas e Métricas</h2>
            <div class="emphasis-block">
                <h3>Meta dos Primeiros 90 Dias</h3>
                <p>Alcançar R$ 2.500/mês em receita recorrente com 5 clientes ativos</p>
            </div>

            <div class="stats-grid">
                <div class="stats-card">
                    <h3>100</h3>
                    <p>Leads Gerados</p>
                </div>
                <div class="stats-card">
                    <h3>30</h3>
                    <p>Demonstrações</p>
                </div>
                <div class="stats-card">
                    <h3>15</h3>
                    <p>Reuniões Agendadas</p>
                </div>
                <div class="stats-card">
                    <h3>5</h3>
                    <p>Clientes Fechados</p>
                </div>
            </div>

            <div class="card">
                <h4>Métricas para Acompanhar</h4>
                <div class="grid-2">
                    <div>
                        <ul>
                            <li>Leads por canal</li>
                            <li>Conversão Lead → Demo</li>
                            <li>Conversão Demo → Reunião</li>
                            <li>Conversão Reunião → Venda</li>
                            <li>Ticket médio por cliente</li>
                        </ul>
                    </div>
                    <div>
                        <ul>
                            <li>Churn rate</li>
                            <li>Tempo de implementação</li>
                            <li>Satisfação do cliente</li>
                            <li>Indicações geradas</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="investment-section">
            <h2>💰 Investimento Necessário</h2>
            <div class="grid-2">
                <div class="card">
                    <h4>Tempo Semanal Necessário</h4>
                    <ul>
                        <li>Criação de conteúdo: 4h</li>
                        <li>Publicação e engagement: 3h</li>
                        <li>Reuniões comerciais: 2-4h</li>
                        <li>Follow-up e suporte: 2h</li>
                    </ul>
                    <div class="emphasis-block" style="margin-top: 20px;">
                        <h3>Total: 11-13h/semana</h3>
                    </div>
                </div>
                <div class="card">
                    <h4>Investimento Financeiro</h4>
                    <ul>
                        <li>Landing page: R$ 500 (uma vez)</li>
                        <li>WhatsApp Business: R$ 0</li>
                        <li>Ferramentas de design: R$ 50/mês</li>
                    </ul>
                    <div class="emphasis-block" style="margin-top: 20px;">
                        <h3>Total mensal: ~R$ 50</h3>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>🚀 Próximos Passos Imediatos</h2>
            <div class="card">
                <ul class="checklist">
                    <li>Definir nome da sub-marca</li>
                    <li>Gravar vídeo de 60s demonstrando o agente</li>
                    <li>Criar perfil LinkedIn otimizado</li>
                    <li>Preparar os primeiros 5 posts</li>
                    <li>Testar com 1-2 dentistas conhecidos</li>
                </ul>
            </div>
        </section>
    </div>

    <div class="footer">
        <h3>Sucesso na Implementação!</h3>
        <p>Este plano estratégico foi desenvolvido para maximizar suas chances de sucesso no mercado odontológico.</p>
    </div>
</body>

</html>